import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Trophy, Plus, Play, BarChart3 } from 'lucide-react'
import { LandingPage } from './LandingPage'
import { Dashboard } from './Dashboard'
import { StudyMode } from './StudyMode'
import { CreateDeck } from './CreateDeck'

export type Screen = 'landing' | 'dashboard' | 'study' | 'create' | 'stats'

export interface Flashcard {
  id: string
  front: string
  back: string
  difficulty: number
  lastReviewed?: Date
  nextReview?: Date
}

export interface Deck {
  id: string
  title: string
  description: string
  cards: Flashcard[]
  created: Date
  lastStudied?: Date
}

export function FlashcardApp() {
  const [currentScreen, setCurrentScreen] = useState<Screen>('landing')
  const [decks, setDecks] = useState<Deck[]>([
    {
      id: '1',
      title: 'Vocabulario en Inglés',
      description: 'Palabras básicas para principiantes',
      cards: [
        { id: '1', front: 'Hello', back: 'Ho<PERSON>', difficulty: 1 },
        { id: '2', front: 'Goodbye', back: '<PERSON><PERSON><PERSON>', difficulty: 1 },
        { id: '3', front: 'Thank you', back: '<PERSON><PERSON><PERSON>', difficulty: 1 },
        { id: '4', front: 'Please', back: 'Por favor', difficulty: 1 },
        { id: '5', front: 'Excuse me', back: 'Disculpe', difficulty: 1 },
      ],
      created: new Date(),
    },
    {
      id: '2',
      title: 'Fórmulas Matemáticas',
      description: 'Fórmulas básicas de álgebra',
      cards: [
        { id: '6', front: '(a + b)²', back: 'a² + 2ab + b²', difficulty: 2 },
        { id: '7', front: '(a - b)²', back: 'a² - 2ab + b²', difficulty: 2 },
        { id: '8', front: 'a² - b²', back: '(a + b)(a - b)', difficulty: 2 },
      ],
      created: new Date(),
    }
  ])
  const [currentDeck, setCurrentDeck] = useState<Deck | null>(null)

  const addDeck = (deck: Omit<Deck, 'id' | 'created'>) => {
    const newDeck: Deck = {
      ...deck,
      id: Date.now().toString(),
      created: new Date(),
    }
    setDecks([...decks, newDeck])
    setCurrentScreen('dashboard')
  }

  const startStudy = (deck: Deck) => {
    setCurrentDeck(deck)
    setCurrentScreen('study')
  }

  const goToScreen = (screen: Screen) => {
    setCurrentScreen(screen)
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case 'landing':
        return <LandingPage onGetStarted={() => goToScreen('dashboard')} />
      case 'dashboard':
        return (
          <Dashboard 
            decks={decks} 
            onStartStudy={startStudy}
            onCreateDeck={() => goToScreen('create')}
            onViewStats={() => goToScreen('stats')}
          />
        )
      case 'study':
        return currentDeck ? (
          <StudyMode 
            deck={currentDeck} 
            onFinish={() => goToScreen('dashboard')}
          />
        ) : (
          <Dashboard 
            decks={decks} 
            onStartStudy={startStudy}
            onCreateDeck={() => goToScreen('create')}
            onViewStats={() => goToScreen('stats')}
          />
        )
      case 'create':
        return (
          <CreateDeck 
            onSave={addDeck}
            onCancel={() => goToScreen('dashboard')}
          />
        )
      default:
        return <LandingPage onGetStarted={() => goToScreen('dashboard')} />
    }
  }

  return (
    <div className="app-container">
      {currentScreen !== 'landing' && (
        <header className="header">
          <div className="logo">
            <BookOpen size={24} />
            FlashCards Pro
          </div>
          <nav style={{ display: 'flex', gap: '1rem' }}>
            <button 
              className="btn btn-secondary"
              onClick={() => goToScreen('dashboard')}
            >
              <BarChart3 size={16} />
              Dashboard
            </button>
            <button 
              className="btn btn-secondary"
              onClick={() => goToScreen('create')}
            >
              <Plus size={16} />
              Crear Mazo
            </button>
          </nav>
        </header>
      )}
      <main className="main-content">
        {renderScreen()}
      </main>
    </div>
  )
}
