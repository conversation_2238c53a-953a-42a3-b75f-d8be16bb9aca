'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuthStore } from '@/store/useAuthStore'
import { useDecksStore } from '@/store/useDecksStore'
import { X, BookOpen, Tag, FileText } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface CreateDeckModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CreateDeckModal({ isOpen, onClose }: CreateDeckModalProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [category, setCategory] = useState('')
  const [tags, setTags] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { user } = useAuthStore()
  const { createDeck } = useDecksStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setLoading(true)
    setError('')

    try {
      const tagsArray = tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)

      await createDeck({
        user_id: user.id,
        title: title.trim(),
        description: description.trim() || null,
        category: category.trim() || null,
        tags: tagsArray,
        is_public: false,
        is_premium: false,
      })

      // Reset form
      setTitle('')
      setDescription('')
      setCategory('')
      setTags('')
      onClose()
    } catch (err: any) {
      setError(err.message || 'Ha ocurrido un error al crear el mazo')
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    'Idiomas',
    'Ciencias',
    'Matemáticas',
    'Historia',
    'Literatura',
    'Medicina',
    'Programación',
    'Arte',
    'Música',
    'Deportes',
    'Geografía',
    'Filosofía',
    'Otro'
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <CardHeader className="relative">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
          <CardTitle className="text-2xl flex items-center">
            <BookOpen className="h-6 w-6 mr-2 text-blue-600" />
            Crear Nuevo Mazo
          </CardTitle>
          <CardDescription>
            Crea un nuevo mazo de flashcards para organizar tu aprendizaje
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Title */}
            <div className="space-y-2">
              <label htmlFor="title" className="text-sm font-medium">
                Título del Mazo *
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="title"
                  type="text"
                  placeholder="Ej: Vocabulario en Inglés"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="pl-10"
                  required
                  maxLength={100}
                />
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Descripción (Opcional)
              </label>
              <textarea
                id="description"
                placeholder="Describe brevemente el contenido de este mazo..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={3}
                maxLength={500}
              />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium">
                Categoría (Opcional)
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Seleccionar categoría...</option>
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <label htmlFor="tags" className="text-sm font-medium">
                Etiquetas (Opcional)
              </label>
              <div className="relative">
                <Tag className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="tags"
                  type="text"
                  placeholder="Ej: básico, principiante, examen (separadas por comas)"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="pl-10"
                />
              </div>
              <p className="text-xs text-gray-500">
                Separa las etiquetas con comas para facilitar la búsqueda
              </p>
            </div>

            {error && (
              <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded">
                {error}
              </div>
            )}

            <div className="flex space-x-3 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                className="flex-1"
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button 
                type="submit" 
                className="flex-1" 
                disabled={loading || !title.trim()}
              >
                {loading ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : null}
                Crear Mazo
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
