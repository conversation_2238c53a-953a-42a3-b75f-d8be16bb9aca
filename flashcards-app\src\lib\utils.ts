import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export function calculateNextReview(difficulty: number, interval: number): Date {
  // Algoritmo de repetición espaciada simplificado
  let nextInterval: number
  
  switch (difficulty) {
    case 1: // Muy difícil
      nextInterval = Math.max(1, Math.floor(interval * 0.6))
      break
    case 2: // Difícil
      nextInterval = Math.max(1, Math.floor(interval * 0.8))
      break
    case 3: // Normal
      nextInterval = Math.floor(interval * 1.3)
      break
    case 4: // Fácil
      nextInterval = Math.floor(interval * 2.0)
      break
    case 5: // Muy fácil
      nextInterval = Math.floor(interval * 2.5)
      break
    default:
      nextInterval = 1
  }
  
  const nextReview = new Date()
  nextReview.setDate(nextReview.getDate() + nextInterval)
  return nextReview
}
