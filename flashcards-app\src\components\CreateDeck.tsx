import { useState } from 'react'
import { Plus, Trash2, Save, X } from 'lucide-react'
import { Deck, Flashcard } from './FlashcardApp'

interface CreateDeckProps {
  onSave: (deck: Omit<Deck, 'id' | 'created'>) => void
  onCancel: () => void
}

export function CreateDeck({ onSave, onCancel }: CreateDeckProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [cards, setCards] = useState<Omit<Flashcard, 'id'>[]>([
    { front: '', back: '', difficulty: 1 }
  ])

  const addCard = () => {
    setCards([...cards, { front: '', back: '', difficulty: 1 }])
  }

  const removeCard = (index: number) => {
    if (cards.length > 1) {
      setCards(cards.filter((_, i) => i !== index))
    }
  }

  const updateCard = (index: number, field: 'front' | 'back', value: string) => {
    const newCards = [...cards]
    newCards[index] = { ...newCards[index], [field]: value }
    setCards(newCards)
  }

  const handleSave = () => {
    if (!title.trim()) {
      alert('Por favor, ingresa un título para el mazo')
      return
    }

    const validCards = cards.filter(card => card.front.trim() && card.back.trim())
    
    if (validCards.length === 0) {
      alert('Por favor, agrega al menos una tarjeta válida')
      return
    }

    const deck: Omit<Deck, 'id' | 'created'> = {
      title: title.trim(),
      description: description.trim(),
      cards: validCards.map((card, index) => ({
        ...card,
        id: `card-${Date.now()}-${index}`
      }))
    }

    onSave(deck)
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem',
        color: 'white'
      }}>
        <h1 style={{ fontSize: '2rem', fontWeight: 'bold' }}>Crear Nuevo Mazo</h1>
        <button 
          className="btn btn-secondary"
          onClick={onCancel}
        >
          <X size={16} />
          Cancelar
        </button>
      </div>

      {/* Deck Info */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <h2 style={{ 
          fontSize: '1.25rem', 
          marginBottom: '1rem', 
          color: '#1f2937',
          fontWeight: 'bold'
        }}>
          Información del Mazo
        </h2>
        
        <div style={{ marginBottom: '1rem' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '0.5rem', 
            fontWeight: '600',
            color: '#374151'
          }}>
            Título *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Ej: Vocabulario en Inglés"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '1rem',
              outline: 'none',
              transition: 'border-color 0.2s'
            }}
            onFocus={(e) => e.target.style.borderColor = '#667eea'}
            onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
          />
        </div>

        <div>
          <label style={{ 
            display: 'block', 
            marginBottom: '0.5rem', 
            fontWeight: '600',
            color: '#374151'
          }}>
            Descripción
          </label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe brevemente el contenido de este mazo..."
            rows={3}
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '1rem',
              outline: 'none',
              transition: 'border-color 0.2s',
              resize: 'vertical'
            }}
            onFocus={(e) => e.target.style.borderColor = '#667eea'}
            onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
          />
        </div>
      </div>

      {/* Cards */}
      <div>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '1.5rem'
        }}>
          <h2 style={{ 
            fontSize: '1.25rem', 
            color: 'white',
            fontWeight: 'bold'
          }}>
            Tarjetas ({cards.length})
          </h2>
          <button 
            className="btn btn-primary"
            onClick={addCard}
          >
            <Plus size={16} />
            Agregar Tarjeta
          </button>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {cards.map((card, index) => (
            <div key={index} className="card">
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <h3 style={{ 
                  fontSize: '1rem', 
                  fontWeight: '600',
                  color: '#1f2937'
                }}>
                  Tarjeta {index + 1}
                </h3>
                {cards.length > 1 && (
                  <button 
                    onClick={() => removeCard(index)}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: '#ef4444',
                      cursor: 'pointer',
                      padding: '0.25rem',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>

              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr', 
                gap: '1rem'
              }}>
                <div>
                  <label style={{ 
                    display: 'block', 
                    marginBottom: '0.5rem', 
                    fontWeight: '600',
                    color: '#374151'
                  }}>
                    Frente *
                  </label>
                  <textarea
                    value={card.front}
                    onChange={(e) => updateCard(index, 'front', e.target.value)}
                    placeholder="Pregunta o término..."
                    rows={3}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      outline: 'none',
                      transition: 'border-color 0.2s',
                      resize: 'vertical'
                    }}
                    onFocus={(e) => e.target.style.borderColor = '#667eea'}
                    onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                  />
                </div>

                <div>
                  <label style={{ 
                    display: 'block', 
                    marginBottom: '0.5rem', 
                    fontWeight: '600',
                    color: '#374151'
                  }}>
                    Reverso *
                  </label>
                  <textarea
                    value={card.back}
                    onChange={(e) => updateCard(index, 'back', e.target.value)}
                    placeholder="Respuesta o definición..."
                    rows={3}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      outline: 'none',
                      transition: 'border-color 0.2s',
                      resize: 'vertical'
                    }}
                    onFocus={(e) => e.target.style.borderColor = '#667eea'}
                    onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Save Button */}
      <div style={{ 
        marginTop: '2rem', 
        textAlign: 'center',
        paddingBottom: '2rem'
      }}>
        <button 
          className="btn btn-primary"
          onClick={handleSave}
          style={{ fontSize: '1.1rem', padding: '1rem 2rem' }}
        >
          <Save size={16} />
          Guardar Mazo
        </button>
      </div>
    </div>
  )
}
