import { create } from 'zustand'
import { supabase } from '@/lib/supabase'
import { Deck, DeckWithStats, Flashcard } from '@/types'

interface DecksState {
  decks: DeckWithStats[]
  currentDeck: Deck | null
  loading: boolean
  fetchDecks: (userId: string) => Promise<void>
  createDeck: (deck: Omit<Deck, 'id' | 'created_at' | 'updated_at'>) => Promise<Deck>
  updateDeck: (id: string, updates: Partial<Deck>) => Promise<void>
  deleteDeck: (id: string) => Promise<void>
  setCurrentDeck: (deck: Deck | null) => void
  getDeckStats: (deckId: string) => Promise<DeckWithStats | null>
}

export const useDecksStore = create<DecksState>((set, get) => ({
  decks: [],
  currentDeck: null,
  loading: false,

  fetchDecks: async (userId: string) => {
    set({ loading: true })
    try {
      const { data: decks, error } = await supabase
        .from('decks')
        .select(`
          *,
          flashcards(count)
        `)
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })

      if (error) throw error

      // Obtener estadísticas para cada mazo
      const decksWithStats: DeckWithStats[] = await Promise.all(
        (decks || []).map(async (deck) => {
          const { data: cardCount } = await supabase
            .from('flashcards')
            .select('id', { count: 'exact' })
            .eq('deck_id', deck.id)

          const { data: progressData } = await supabase
            .from('card_progress')
            .select('*')
            .eq('user_id', userId)
            .in('card_id', 
              await supabase
                .from('flashcards')
                .select('id')
                .eq('deck_id', deck.id)
                .then(({ data }) => data?.map(card => card.id) || [])
            )

          const masteredCount = progressData?.filter(p => p.ease_factor >= 2.5).length || 0
          const dueCount = progressData?.filter(p => new Date(p.next_review) <= new Date()).length || 0

          return {
            ...deck,
            card_count: cardCount?.length || 0,
            mastered_count: masteredCount,
            due_count: dueCount,
          }
        })
      )

      set({ decks: decksWithStats })
    } catch (error) {
      console.error('Error fetching decks:', error)
    } finally {
      set({ loading: false })
    }
  },

  createDeck: async (deckData) => {
    const { data, error } = await supabase
      .from('decks')
      .insert(deckData)
      .select()
      .single()

    if (error) throw error

    const newDeck: DeckWithStats = {
      ...data,
      card_count: 0,
      mastered_count: 0,
      due_count: 0,
    }

    set(state => ({ decks: [newDeck, ...state.decks] }))
    return data
  },

  updateDeck: async (id: string, updates: Partial<Deck>) => {
    const { data, error } = await supabase
      .from('decks')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    set(state => ({
      decks: state.decks.map(deck => 
        deck.id === id ? { ...deck, ...data } : deck
      ),
      currentDeck: state.currentDeck?.id === id 
        ? { ...state.currentDeck, ...data } 
        : state.currentDeck
    }))
  },

  deleteDeck: async (id: string) => {
    const { error } = await supabase
      .from('decks')
      .delete()
      .eq('id', id)

    if (error) throw error

    set(state => ({
      decks: state.decks.filter(deck => deck.id !== id),
      currentDeck: state.currentDeck?.id === id ? null : state.currentDeck
    }))
  },

  setCurrentDeck: (deck: Deck | null) => {
    set({ currentDeck: deck })
  },

  getDeckStats: async (deckId: string) => {
    const { decks } = get()
    return decks.find(deck => deck.id === deckId) || null
  },
}))
