import { create } from 'zustand'
import { supabase } from '@/lib/supabase'
import { Flashcard, FlashcardWithProgress, CardProgress } from '@/types'
import { calculateNextReview } from '@/lib/utils'

interface FlashcardsState {
  flashcards: FlashcardWithProgress[]
  loading: boolean
  fetchFlashcards: (deckId: string, userId?: string) => Promise<void>
  createFlashcard: (flashcard: Omit<Flashcard, 'id' | 'created_at' | 'updated_at'>) => Promise<Flashcard>
  updateFlashcard: (id: string, updates: Partial<Flashcard>) => Promise<void>
  deleteFlashcard: (id: string) => Promise<void>
  updateCardProgress: (cardId: string, userId: string, difficulty: number) => Promise<void>
  getDueCards: (deckId: string, userId: string) => Promise<FlashcardWithProgress[]>
  getRandomCards: (deckId: string, count: number) => Promise<Flashcard[]>
}

export const useFlashcardsStore = create<FlashcardsState>((set, get) => ({
  flashcards: [],
  loading: false,

  fetchFlashcards: async (deckId: string, userId?: string) => {
    set({ loading: true })
    try {
      const { data: flashcards, error } = await supabase
        .from('flashcards')
        .select('*')
        .eq('deck_id', deckId)
        .order('created_at', { ascending: true })

      if (error) throw error

      let flashcardsWithProgress: FlashcardWithProgress[] = flashcards || []

      if (userId) {
        // Obtener progreso de cada tarjeta
        const { data: progressData } = await supabase
          .from('card_progress')
          .select('*')
          .eq('user_id', userId)
          .in('card_id', flashcards?.map(card => card.id) || [])

        flashcardsWithProgress = (flashcards || []).map(card => ({
          ...card,
          progress: progressData?.find(p => p.card_id === card.id)
        }))
      }

      set({ flashcards: flashcardsWithProgress })
    } catch (error) {
      console.error('Error fetching flashcards:', error)
    } finally {
      set({ loading: false })
    }
  },

  createFlashcard: async (flashcardData) => {
    const { data, error } = await supabase
      .from('flashcards')
      .insert(flashcardData)
      .select()
      .single()

    if (error) throw error

    set(state => ({ 
      flashcards: [...state.flashcards, data] 
    }))
    
    return data
  },

  updateFlashcard: async (id: string, updates: Partial<Flashcard>) => {
    const { data, error } = await supabase
      .from('flashcards')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    set(state => ({
      flashcards: state.flashcards.map(card => 
        card.id === id ? { ...card, ...data } : card
      )
    }))
  },

  deleteFlashcard: async (id: string) => {
    const { error } = await supabase
      .from('flashcards')
      .delete()
      .eq('id', id)

    if (error) throw error

    set(state => ({
      flashcards: state.flashcards.filter(card => card.id !== id)
    }))
  },

  updateCardProgress: async (cardId: string, userId: string, difficulty: number) => {
    // Obtener progreso actual
    const { data: currentProgress } = await supabase
      .from('card_progress')
      .select('*')
      .eq('card_id', cardId)
      .eq('user_id', userId)
      .single()

    const now = new Date().toISOString()
    let newProgress: Partial<CardProgress>

    if (currentProgress) {
      // Actualizar progreso existente
      const newInterval = Math.max(1, currentProgress.interval * (difficulty >= 3 ? 1.3 : 0.6))
      const newEaseFactor = Math.max(1.3, currentProgress.ease_factor + (0.1 - (5 - difficulty) * (0.08 + (5 - difficulty) * 0.02)))
      
      newProgress = {
        ease_factor: newEaseFactor,
        interval: Math.round(newInterval),
        repetitions: difficulty >= 3 ? currentProgress.repetitions + 1 : 0,
        next_review: calculateNextReview(difficulty, newInterval).toISOString(),
        last_reviewed: now,
        times_seen: currentProgress.times_seen + 1,
        times_correct: difficulty >= 3 ? currentProgress.times_correct + 1 : currentProgress.times_correct,
        updated_at: now,
      }

      const { error } = await supabase
        .from('card_progress')
        .update(newProgress)
        .eq('id', currentProgress.id)

      if (error) throw error
    } else {
      // Crear nuevo progreso
      newProgress = {
        user_id: userId,
        card_id: cardId,
        ease_factor: 2.5,
        interval: difficulty >= 3 ? 1 : 0,
        repetitions: difficulty >= 3 ? 1 : 0,
        next_review: calculateNextReview(difficulty, 1).toISOString(),
        last_reviewed: now,
        times_seen: 1,
        times_correct: difficulty >= 3 ? 1 : 0,
      }

      const { error } = await supabase
        .from('card_progress')
        .insert(newProgress)

      if (error) throw error
    }

    // Actualizar estado local
    set(state => ({
      flashcards: state.flashcards.map(card => 
        card.id === cardId 
          ? { ...card, progress: { ...currentProgress, ...newProgress } as CardProgress }
          : card
      )
    }))
  },

  getDueCards: async (deckId: string, userId: string) => {
    const { data: dueCards, error } = await supabase
      .from('flashcards')
      .select(`
        *,
        card_progress!inner(*)
      `)
      .eq('deck_id', deckId)
      .eq('card_progress.user_id', userId)
      .lte('card_progress.next_review', new Date().toISOString())

    if (error) throw error
    return dueCards || []
  },

  getRandomCards: async (deckId: string, count: number) => {
    const { data: cards, error } = await supabase
      .from('flashcards')
      .select('*')
      .eq('deck_id', deckId)
      .limit(count)

    if (error) throw error
    return cards || []
  },
}))
