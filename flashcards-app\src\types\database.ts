export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'premium'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
      }
      decks: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          category: string | null
          tags: string[]
          is_public: boolean
          is_premium: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          category?: string | null
          tags?: string[]
          is_public?: boolean
          is_premium?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          category?: string | null
          tags?: string[]
          is_public?: boolean
          is_premium?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      flashcards: {
        Row: {
          id: string
          deck_id: string
          front_text: string
          back_text: string
          front_image_url: string | null
          back_image_url: string | null
          front_audio_url: string | null
          back_audio_url: string | null
          card_type: 'basic' | 'multiple_choice' | 'fill_blank' | 'true_false'
          options: string[] | null
          correct_answer: string | null
          difficulty: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          deck_id: string
          front_text: string
          back_text: string
          front_image_url?: string | null
          back_image_url?: string | null
          front_audio_url?: string | null
          back_audio_url?: string | null
          card_type?: 'basic' | 'multiple_choice' | 'fill_blank' | 'true_false'
          options?: string[] | null
          correct_answer?: string | null
          difficulty?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          deck_id?: string
          front_text?: string
          back_text?: string
          front_image_url?: string | null
          back_image_url?: string | null
          front_audio_url?: string | null
          back_audio_url?: string | null
          card_type?: 'basic' | 'multiple_choice' | 'fill_blank' | 'true_false'
          options?: string[] | null
          correct_answer?: string | null
          difficulty?: number
          created_at?: string
          updated_at?: string
        }
      }
      study_sessions: {
        Row: {
          id: string
          user_id: string
          deck_id: string
          cards_studied: number
          cards_correct: number
          time_spent: number
          session_type: 'study' | 'quiz' | 'game'
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          deck_id: string
          cards_studied: number
          cards_correct: number
          time_spent: number
          session_type: 'study' | 'quiz' | 'game'
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          deck_id?: string
          cards_studied?: number
          cards_correct?: number
          time_spent?: number
          session_type?: 'study' | 'quiz' | 'game'
          created_at?: string
        }
      }
      card_progress: {
        Row: {
          id: string
          user_id: string
          card_id: string
          ease_factor: number
          interval: number
          repetitions: number
          next_review: string
          last_reviewed: string | null
          times_seen: number
          times_correct: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          card_id: string
          ease_factor?: number
          interval?: number
          repetitions?: number
          next_review: string
          last_reviewed?: string | null
          times_seen?: number
          times_correct?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          card_id?: string
          ease_factor?: number
          interval?: number
          repetitions?: number
          next_review?: string
          last_reviewed?: string | null
          times_seen?: number
          times_correct?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
