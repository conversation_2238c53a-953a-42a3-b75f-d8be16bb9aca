'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DeckWithStats } from '@/types'
import { formatDate } from '@/lib/utils'
import { 
  Play, 
  Edit, 
  MoreVertical, 
  BookOpen, 
  Target, 
  Clock,
  Share,
  Trash2
} from 'lucide-react'

interface DeckCardProps {
  deck: DeckWithStats
}

export function DeckCard({ deck }: DeckCardProps) {
  const [showMenu, setShowMenu] = useState(false)

  const progressPercentage = deck.card_count > 0 
    ? Math.round((deck.mastered_count / deck.card_count) * 100) 
    : 0

  const handleStudy = () => {
    // Navigate to study mode
    console.log('Study deck:', deck.id)
  }

  const handleEdit = () => {
    // Navigate to edit mode
    console.log('Edit deck:', deck.id)
  }

  const handleShare = () => {
    // Share deck functionality
    console.log('Share deck:', deck.id)
  }

  const handleDelete = () => {
    // Delete deck functionality
    console.log('Delete deck:', deck.id)
  }

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-2 group-hover:text-blue-600 transition-colors">
              {deck.title}
            </CardTitle>
            {deck.description && (
              <CardDescription className="mt-1 line-clamp-2">
                {deck.description}
              </CardDescription>
            )}
          </div>
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation()
                setShowMenu(!showMenu)
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
            
            {showMenu && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-10">
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEdit()
                      setShowMenu(false)
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Editar
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleShare()
                      setShowMenu(false)
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Share className="h-4 w-4 mr-2" />
                    Compartir
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete()
                      setShowMenu(false)
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Eliminar
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tags */}
        {deck.tags && deck.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {deck.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full"
              >
                {tag}
              </span>
            ))}
            {deck.tags.length > 3 && (
              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                +{deck.tags.length - 3}
              </span>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <BookOpen className="h-4 w-4 text-blue-600 mr-1" />
              <span className="text-sm font-medium text-gray-900">
                {deck.card_count}
              </span>
            </div>
            <p className="text-xs text-gray-500">Tarjetas</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm font-medium text-gray-900">
                {deck.mastered_count}
              </span>
            </div>
            <p className="text-xs text-gray-500">Dominadas</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Clock className="h-4 w-4 text-orange-600 mr-1" />
              <span className="text-sm font-medium text-gray-900">
                {deck.due_count}
              </span>
            </div>
            <p className="text-xs text-gray-500">Pendientes</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-gray-700">Progreso</span>
            <span className="text-sm text-gray-500">{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Action Button */}
        <Button 
          onClick={handleStudy}
          className="w-full"
          variant={deck.due_count > 0 ? "default" : "outline"}
        >
          <Play className="h-4 w-4 mr-2" />
          {deck.due_count > 0 ? `Estudiar (${deck.due_count})` : 'Repasar'}
        </Button>

        {/* Last studied */}
        {deck.last_studied && (
          <p className="text-xs text-gray-500 text-center mt-2">
            Último estudio: {formatDate(new Date(deck.last_studied))}
          </p>
        )}
      </CardContent>

      {/* Overlay for menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowMenu(false)}
        />
      )}
    </Card>
  )
}
