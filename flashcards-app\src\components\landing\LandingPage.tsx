'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AuthModal } from '@/components/auth/AuthModal'
import { 
  BookOpen, 
  Brain, 
  Trophy, 
  Users, 
  Zap, 
  Target,
  Clock,
  BarChart3
} from 'lucide-react'

export function LandingPage() {
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthMode(mode)
    setShowAuthModal(true)
  }

  const features = [
    {
      icon: Brain,
      title: "Repetición Espaciada",
      description: "Algoritmo inteligente que optimiza tu aprendizaje mostrando las tarjetas en el momento perfecto."
    },
    {
      icon: BookOpen,
      title: "Contenido Multimedia",
      description: "Crea flashcards con texto, imágenes, audio y videos para adaptarse a tu estilo de aprendizaje."
    },
    {
      icon: Trophy,
      title: "Juegos y Quizzes",
      description: "Convierte el estudio en diversión con mini-juegos, desafíos y competencias."
    },
    {
      icon: BarChart3,
      title: "Seguimiento de Progreso",
      description: "Estadísticas detalladas y visuales para monitorear tu avance y identificar áreas de mejora."
    },
    {
      icon: Users,
      title: "Colaboración",
      description: "Comparte mazos con amigos, colabora en tiempo real y accede a contenido de la comunidad."
    },
    {
      icon: Zap,
      title: "Plantillas Rápidas",
      description: "Plantillas predefinidas para vocabulario, fórmulas y definiciones que ahorran tiempo."
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">FlashCards Pro</span>
          </div>
          <div className="space-x-4">
            <Button 
              variant="ghost" 
              onClick={() => handleAuthClick('signin')}
            >
              Iniciar Sesión
            </Button>
            <Button 
              onClick={() => handleAuthClick('signup')}
            >
              Registrarse
            </Button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          Aprende de manera <span className="text-blue-600">inteligente</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          La aplicación de flashcards más completa con repetición espaciada, 
          contenido multimedia y seguimiento de progreso para maximizar tu aprendizaje.
        </p>
        <div className="space-x-4">
          <Button 
            size="lg" 
            onClick={() => handleAuthClick('signup')}
            className="text-lg px-8 py-3"
          >
            Comenzar Gratis
          </Button>
          <Button 
            variant="outline" 
            size="lg"
            className="text-lg px-8 py-3"
          >
            Ver Demo
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
          Características Principales
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-blue-100">Usuarios Activos</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">1M+</div>
              <div className="text-blue-100">Flashcards Creadas</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">95%</div>
              <div className="text-blue-100">Mejora en Retención</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-blue-100">Idiomas Soportados</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-6">
          ¿Listo para revolucionar tu forma de estudiar?
        </h2>
        <p className="text-xl text-gray-600 mb-8">
          Únete a miles de estudiantes que ya están aprendiendo de manera más eficiente.
        </p>
        <Button 
          size="lg" 
          onClick={() => handleAuthClick('signup')}
          className="text-lg px-8 py-3"
        >
          Comenzar Ahora - Es Gratis
        </Button>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <BookOpen className="h-6 w-6" />
            <span className="text-xl font-bold">FlashCards Pro</span>
          </div>
          <p className="text-gray-400">
            © 2024 FlashCards Pro. Todos los derechos reservados.
          </p>
        </div>
      </footer>

      {/* Auth Modal */}
      <AuthModal 
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  )
}
