import { useState, useEffect } from 'react'
import { RotateCcw, ArrowLeft, ArrowRight, CheckCircle, XCircle, Eye } from 'lucide-react'
import { Deck, Flashcard } from './FlashcardApp'

interface StudyModeProps {
  deck: Deck
  onFinish: () => void
}

export function StudyMode({ deck, onFinish }: StudyModeProps) {
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [showAnswer, setShowAnswer] = useState(false)
  const [studiedCards, setStudiedCards] = useState<Set<string>>(new Set())
  const [correctCards, setCorrectCards] = useState<Set<string>>(new Set())
  const [startTime] = useState(new Date())

  const currentCard = deck.cards[currentCardIndex]
  const progress = ((currentCardIndex + 1) / deck.cards.length) * 100

  const nextCard = () => {
    if (currentCardIndex < deck.cards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1)
      setShowAnswer(false)
    }
  }

  const prevCard = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1)
      setShowAnswer(false)
    }
  }

  const markCard = (correct: boolean) => {
    const cardId = currentCard.id
    setStudiedCards(prev => new Set([...prev, cardId]))
    
    if (correct) {
      setCorrectCards(prev => new Set([...prev, cardId]))
    } else {
      setCorrectCards(prev => {
        const newSet = new Set(prev)
        newSet.delete(cardId)
        return newSet
      })
    }

    // Auto-advance to next card
    setTimeout(() => {
      if (currentCardIndex < deck.cards.length - 1) {
        nextCard()
      }
    }, 500)
  }

  const resetCard = () => {
    setShowAnswer(false)
  }

  const isFinished = studiedCards.size === deck.cards.length
  const accuracy = studiedCards.size > 0 ? (correctCards.size / studiedCards.size) * 100 : 0

  if (isFinished) {
    const endTime = new Date()
    const studyTime = Math.round((endTime.getTime() - startTime.getTime()) / 1000 / 60)

    return (
      <div style={{ maxWidth: '600px', margin: '0 auto', textAlign: 'center' }}>
        <div className="card">
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎉</div>
          <h1 style={{ 
            fontSize: '2rem', 
            marginBottom: '1rem', 
            color: '#1f2937',
            fontWeight: 'bold'
          }}>
            ¡Sesión Completada!
          </h1>
          <p style={{ 
            fontSize: '1.1rem', 
            color: '#6b7280', 
            marginBottom: '2rem',
            lineHeight: '1.6'
          }}>
            Has terminado de estudiar "{deck.title}"
          </p>

          {/* Stats */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
            gap: '1rem',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#667eea' }}>
                {deck.cards.length}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.9rem' }}>Tarjetas</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>
                {correctCards.size}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.9rem' }}>Correctas</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>
                {Math.round(accuracy)}%
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.9rem' }}>Precisión</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#8b5cf6' }}>
                {studyTime}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.9rem' }}>Minutos</div>
            </div>
          </div>

          <button 
            className="btn btn-primary"
            onClick={onFinish}
            style={{ fontSize: '1.1rem', padding: '1rem 2rem' }}
          >
            Volver al Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem',
        color: 'white'
      }}>
        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{deck.title}</h1>
        <div style={{ fontSize: '1.1rem' }}>
          {currentCardIndex + 1} / {deck.cards.length}
        </div>
      </div>

      {/* Progress Bar */}
      <div style={{ 
        background: 'rgba(255, 255, 255, 0.2)', 
        borderRadius: '10px', 
        height: '8px',
        marginBottom: '2rem',
        overflow: 'hidden'
      }}>
        <div style={{ 
          background: '#10b981', 
          height: '100%', 
          width: `${progress}%`,
          transition: 'width 0.3s ease'
        }} />
      </div>

      {/* Flashcard */}
      <div className="card" style={{ 
        minHeight: '400px', 
        display: 'flex', 
        flexDirection: 'column',
        justifyContent: 'center',
        textAlign: 'center',
        position: 'relative',
        cursor: 'pointer'
      }} onClick={() => setShowAnswer(!showAnswer)}>
        <div style={{ marginBottom: '2rem' }}>
          <div style={{ 
            fontSize: '0.9rem', 
            color: '#6b7280', 
            marginBottom: '1rem',
            textTransform: 'uppercase',
            letterSpacing: '1px'
          }}>
            {showAnswer ? 'Respuesta' : 'Pregunta'}
          </div>
          <div style={{ 
            fontSize: '2rem', 
            fontWeight: 'bold', 
            color: '#1f2937',
            lineHeight: '1.4',
            minHeight: '120px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {showAnswer ? currentCard.back : currentCard.front}
          </div>
        </div>

        {!showAnswer && (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            gap: '0.5rem',
            color: '#6b7280',
            fontSize: '0.9rem'
          }}>
            <Eye size={16} />
            Haz clic para ver la respuesta
          </div>
        )}
      </div>

      {/* Controls */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginTop: '2rem',
        gap: '1rem'
      }}>
        <button 
          className="btn btn-secondary"
          onClick={prevCard}
          disabled={currentCardIndex === 0}
          style={{ opacity: currentCardIndex === 0 ? 0.5 : 1 }}
        >
          <ArrowLeft size={16} />
          Anterior
        </button>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button 
            className="btn btn-secondary"
            onClick={resetCard}
          >
            <RotateCcw size={16} />
            Reset
          </button>

          {showAnswer && (
            <>
              <button 
                className="btn"
                onClick={() => markCard(false)}
                style={{ 
                  background: '#ef4444', 
                  color: 'white',
                  border: 'none'
                }}
              >
                <XCircle size={16} />
                Incorrecto
              </button>
              <button 
                className="btn"
                onClick={() => markCard(true)}
                style={{ 
                  background: '#10b981', 
                  color: 'white',
                  border: 'none'
                }}
              >
                <CheckCircle size={16} />
                Correcto
              </button>
            </>
          )}
        </div>

        <button 
          className="btn btn-secondary"
          onClick={nextCard}
          disabled={currentCardIndex === deck.cards.length - 1}
          style={{ opacity: currentCardIndex === deck.cards.length - 1 ? 0.5 : 1 }}
        >
          Siguiente
          <ArrowRight size={16} />
        </button>
      </div>

      {/* Study Progress */}
      <div style={{ 
        marginTop: '2rem', 
        textAlign: 'center',
        color: 'rgba(255, 255, 255, 0.8)'
      }}>
        <div style={{ marginBottom: '0.5rem' }}>
          Estudiadas: {studiedCards.size} / {deck.cards.length}
        </div>
        <div>
          Correctas: {correctCards.size} ({Math.round(accuracy)}%)
        </div>
      </div>
    </div>
  )
}
