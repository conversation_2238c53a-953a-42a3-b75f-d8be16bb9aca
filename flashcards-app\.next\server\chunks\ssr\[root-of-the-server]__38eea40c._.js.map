{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { Profile } from '@/types'\n\n// Mock user type for demo\ninterface MockUser {\n  id: string\n  email: string\n}\n\ninterface AuthState {\n  user: MockUser | null\n  profile: Profile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<void>\n  signUp: (email: string, password: string, fullName: string) => Promise<void>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<Profile>) => Promise<void>\n  initialize: () => Promise<void>\n}\n\nexport const useAuthStore = create<AuthState>((set, get) => ({\n  user: null,\n  profile: null,\n  loading: true,\n\n  signIn: async (email: string, password: string) => {\n    // Mock authentication for demo\n    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call\n\n    const mockUser: MockUser = {\n      id: '1',\n      email: email\n    }\n\n    const mockProfile: Profile = {\n      id: '1',\n      email: email,\n      full_name: 'Usuario Demo',\n      avatar_url: null,\n      subscription_tier: 'free',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n\n    set({ user: mockUser, profile: mockProfile })\n  },\n\n  signUp: async (email: string, password: string, fullName: string) => {\n    // Mock registration for demo\n    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call\n\n    const mockUser: MockUser = {\n      id: '1',\n      email: email\n    }\n\n    const mockProfile: Profile = {\n      id: '1',\n      email: email,\n      full_name: fullName,\n      avatar_url: null,\n      subscription_tier: 'free',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n\n    set({ user: mockUser, profile: mockProfile })\n  },\n\n  signOut: async () => {\n    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API call\n    set({ user: null, profile: null })\n  },\n\n  updateProfile: async (updates: Partial<Profile>) => {\n    const { profile } = get()\n    if (!profile) throw new Error('No user logged in')\n\n    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API call\n    set({ profile: { ...profile, ...updates } })\n  },\n\n  initialize: async () => {\n    try {\n      // Simulate checking for existing session\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // For demo, we'll start with no user logged in\n      set({ user: null, profile: null })\n    } catch (error) {\n      console.error('Error initializing auth:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAoBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,MAAM;QACN,SAAS;QACT,SAAS;QAET,QAAQ,OAAO,OAAe;YAC5B,+BAA+B;YAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,oBAAoB;;YAE5E,MAAM,WAAqB;gBACzB,IAAI;gBACJ,OAAO;YACT;YAEA,MAAM,cAAuB;gBAC3B,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,IAAI;gBAAE,MAAM;gBAAU,SAAS;YAAY;QAC7C;QAEA,QAAQ,OAAO,OAAe,UAAkB;YAC9C,6BAA6B;YAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,oBAAoB;;YAE5E,MAAM,WAAqB;gBACzB,IAAI;gBACJ,OAAO;YACT;YAEA,MAAM,cAAuB;gBAC3B,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,IAAI;gBAAE,MAAM;gBAAU,SAAS;YAAY;QAC7C;QAEA,SAAS;YACP,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,oBAAoB;;YAC3E,IAAI;gBAAE,MAAM;gBAAM,SAAS;YAAK;QAClC;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,OAAO,EAAE,GAAG;YACpB,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;YAE9B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,oBAAoB;;YAC3E,IAAI;gBAAE,SAAS;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC;YAAE;QAC5C;QAEA,YAAY;YACV,IAAI;gBACF,yCAAyC;gBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,+CAA+C;gBAC/C,IAAI;oBAAE,MAAM;oBAAM,SAAS;gBAAK;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('es-ES', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = seconds % 60\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array]\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1))\n    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]\n  }\n  return shuffled\n}\n\nexport function calculateNextReview(difficulty: number, interval: number): Date {\n  // Algoritmo de repetición espaciada simplificado\n  let nextInterval: number\n  \n  switch (difficulty) {\n    case 1: // Muy difícil\n      nextInterval = Math.max(1, Math.floor(interval * 0.6))\n      break\n    case 2: // Difícil\n      nextInterval = Math.max(1, Math.floor(interval * 0.8))\n      break\n    case 3: // Normal\n      nextInterval = Math.floor(interval * 1.3)\n      break\n    case 4: // Fácil\n      nextInterval = Math.floor(interval * 2.0)\n      break\n    case 5: // Muy fácil\n      nextInterval = Math.floor(interval * 2.5)\n      break\n    default:\n      nextInterval = 1\n  }\n  \n  const nextReview = new Date()\n  nextReview.setDate(nextReview.getDate() + nextInterval)\n  return nextReview\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC1C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IAC1D;IACA,OAAO;AACT;AAEO,SAAS,oBAAoB,UAAkB,EAAE,QAAgB;IACtE,iDAAiD;IACjD,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,WAAW;YACjD;QACF,KAAK;YACH,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,WAAW;YACjD;QACF,KAAK;YACH,eAAe,KAAK,KAAK,CAAC,WAAW;YACrC;QACF,KAAK;YACH,eAAe,KAAK,KAAK,CAAC,WAAW;YACrC;QACF,KAAK;YACH,eAAe,KAAK,KAAK,CAAC,WAAW;YACrC;QACF;YACE,eAAe;IACnB;IAEA,MAAM,aAAa,IAAI;IACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;IAC1C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-6 w-6',\n    lg: 'h-8 w-8'\n  }\n\n  return (\n    <div\n      className={cn(\n        \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\",\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/auth/AuthModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { X, Mail, Lock, User } from 'lucide-react'\nimport { LoadingSpinner } from '@/components/ui/loading-spinner'\n\ninterface AuthModalProps {\n  isOpen: boolean\n  onClose: () => void\n  mode: 'signin' | 'signup'\n  onModeChange: (mode: 'signin' | 'signup') => void\n}\n\nexport function AuthModal({ isOpen, onClose, mode, onModeChange }: AuthModalProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuthStore()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      if (mode === 'signin') {\n        await signIn(email, password)\n      } else {\n        await signUp(email, password, fullName)\n      }\n      onClose()\n      // Reset form\n      setEmail('')\n      setPassword('')\n      setFullName('')\n    } catch (err: any) {\n      setError(err.message || 'Ha ocurrido un error')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"relative\">\n          <button\n            onClick={onClose}\n            className=\"absolute right-4 top-4 text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n          <CardTitle className=\"text-2xl text-center\">\n            {mode === 'signin' ? 'Iniciar Sesión' : 'Crear Cuenta'}\n          </CardTitle>\n          <CardDescription className=\"text-center\">\n            {mode === 'signin' \n              ? 'Accede a tu cuenta para continuar aprendiendo'\n              : 'Únete a FlashCards Pro y comienza a aprender'\n            }\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {mode === 'signup' && (\n              <div className=\"space-y-2\">\n                <label htmlFor=\"fullName\" className=\"text-sm font-medium\">\n                  Nombre Completo\n                </label>\n                <div className=\"relative\">\n                  <User className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                  <Input\n                    id=\"fullName\"\n                    type=\"text\"\n                    placeholder=\"Tu nombre completo\"\n                    value={fullName}\n                    onChange={(e) => setFullName(e.target.value)}\n                    className=\"pl-10\"\n                    required\n                  />\n                </div>\n              </div>\n            )}\n            \n            <div className=\"space-y-2\">\n              <label htmlFor=\"email\" className=\"text-sm font-medium\">\n                Correo Electrónico\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"pl-10\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"text-sm font-medium\">\n                Contraseña\n              </label>\n              <div className=\"relative\">\n                <Lock className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  placeholder=\"••••••••\"\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"pl-10\"\n                  required\n                  minLength={6}\n                />\n              </div>\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm text-center bg-red-50 p-2 rounded\">\n                {error}\n              </div>\n            )}\n\n            <Button \n              type=\"submit\" \n              className=\"w-full\" \n              disabled={loading}\n            >\n              {loading ? (\n                <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n              ) : null}\n              {mode === 'signin' ? 'Iniciar Sesión' : 'Crear Cuenta'}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-sm text-gray-600\">\n              {mode === 'signin' \n                ? '¿No tienes una cuenta? '\n                : '¿Ya tienes una cuenta? '\n              }\n              <button\n                onClick={() => onModeChange(mode === 'signin' ? 'signup' : 'signin')}\n                className=\"text-blue-600 hover:underline font-medium\"\n              >\n                {mode === 'signin' ? 'Regístrate' : 'Inicia sesión'}\n              </button>\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAiBO,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAkB;IAC/E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEtC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,MAAM,OAAO,OAAO;YACtB,OAAO;gBACL,MAAM,OAAO,OAAO,UAAU;YAChC;YACA;YACA,aAAa;YACb,SAAS;YACT,YAAY;YACZ,YAAY;QACd,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB,SAAS,WAAW,mBAAmB;;;;;;sCAE1C,8OAAC,gIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,SAAS,WACN,kDACA;;;;;;;;;;;;8BAIR,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,SAAS,0BACR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAsB;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAsB;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAsB;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,QAAQ;oDACR,WAAW;;;;;;;;;;;;;;;;;;gCAKhB,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;;wCAET,wBACC,8OAAC,8IAAA,CAAA,iBAAc;4CAAC,MAAK;4CAAK,WAAU;;;;;mDAClC;wCACH,SAAS,WAAW,mBAAmB;;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCACV,SAAS,WACN,4BACA;kDAEJ,8OAAC;wCACC,SAAS,IAAM,aAAa,SAAS,WAAW,WAAW;wCAC3D,WAAU;kDAET,SAAS,WAAW,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/landing/LandingPage.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AuthModal } from '@/components/auth/AuthModal'\nimport { \n  BookOpen, \n  Brain, \n  Trophy, \n  Users, \n  Zap, \n  Target,\n  Clock,\n  BarChart3\n} from 'lucide-react'\n\nexport function LandingPage() {\n  const [showAuthModal, setShowAuthModal] = useState(false)\n  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')\n\n  const handleAuthClick = (mode: 'signin' | 'signup') => {\n    setAuthMode(mode)\n    setShowAuthModal(true)\n  }\n\n  const features = [\n    {\n      icon: Brain,\n      title: \"Repetición Espaciada\",\n      description: \"Algoritmo inteligente que optimiza tu aprendizaje mostrando las tarjetas en el momento perfecto.\"\n    },\n    {\n      icon: BookOpen,\n      title: \"Contenido Multimedia\",\n      description: \"Crea flashcards con texto, imágenes, audio y videos para adaptarse a tu estilo de aprendizaje.\"\n    },\n    {\n      icon: Trophy,\n      title: \"Juegos y Quizzes\",\n      description: \"Convierte el estudio en diversión con mini-juegos, desafíos y competencias.\"\n    },\n    {\n      icon: BarChart3,\n      title: \"Seguimiento de Progreso\",\n      description: \"Estadísticas detalladas y visuales para monitorear tu avance y identificar áreas de mejora.\"\n    },\n    {\n      icon: Users,\n      title: \"Colaboración\",\n      description: \"Comparte mazos con amigos, colabora en tiempo real y accede a contenido de la comunidad.\"\n    },\n    {\n      icon: Zap,\n      title: \"Plantillas Rápidas\",\n      description: \"Plantillas predefinidas para vocabulario, fórmulas y definiciones que ahorran tiempo.\"\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"container mx-auto px-4 py-6\">\n        <nav className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-2xl font-bold text-gray-900\">FlashCards Pro</span>\n          </div>\n          <div className=\"space-x-4\">\n            <Button \n              variant=\"ghost\" \n              onClick={() => handleAuthClick('signin')}\n            >\n              Iniciar Sesión\n            </Button>\n            <Button \n              onClick={() => handleAuthClick('signup')}\n            >\n              Registrarse\n            </Button>\n          </div>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n          Aprende de manera <span className=\"text-blue-600\">inteligente</span>\n        </h1>\n        <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n          La aplicación de flashcards más completa con repetición espaciada, \n          contenido multimedia y seguimiento de progreso para maximizar tu aprendizaje.\n        </p>\n        <div className=\"space-x-4\">\n          <Button \n            size=\"lg\" \n            onClick={() => handleAuthClick('signup')}\n            className=\"text-lg px-8 py-3\"\n          >\n            Comenzar Gratis\n          </Button>\n          <Button \n            variant=\"outline\" \n            size=\"lg\"\n            className=\"text-lg px-8 py-3\"\n          >\n            Ver Demo\n          </Button>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"container mx-auto px-4 py-20\">\n        <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n          Características Principales\n        </h2>\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <Card key={index} className=\"text-center hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <feature.icon className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n                <CardTitle className=\"text-xl\">{feature.title}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription className=\"text-gray-600\">\n                  {feature.description}\n                </CardDescription>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"bg-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">10K+</div>\n              <div className=\"text-blue-100\">Usuarios Activos</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">1M+</div>\n              <div className=\"text-blue-100\">Flashcards Creadas</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">95%</div>\n              <div className=\"text-blue-100\">Mejora en Retención</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">50+</div>\n              <div className=\"text-blue-100\">Idiomas Soportados</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"container mx-auto px-4 py-20 text-center\">\n        <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\n          ¿Listo para revolucionar tu forma de estudiar?\n        </h2>\n        <p className=\"text-xl text-gray-600 mb-8\">\n          Únete a miles de estudiantes que ya están aprendiendo de manera más eficiente.\n        </p>\n        <Button \n          size=\"lg\" \n          onClick={() => handleAuthClick('signup')}\n          className=\"text-lg px-8 py-3\"\n        >\n          Comenzar Ahora - Es Gratis\n        </Button>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"flex items-center justify-center space-x-2 mb-4\">\n            <BookOpen className=\"h-6 w-6\" />\n            <span className=\"text-xl font-bold\">FlashCards Pro</span>\n          </div>\n          <p className=\"text-gray-400\">\n            © 2024 FlashCards Pro. Todos los derechos reservados.\n          </p>\n        </div>\n      </footer>\n\n      {/* Auth Modal */}\n      <AuthModal \n        isOpen={showAuthModal}\n        onClose={() => setShowAuthModal(false)}\n        mode={authMode}\n        onModeChange={setAuthMode}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAiBO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,MAAM,kBAAkB,CAAC;QACvB,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,WAAW;QACf;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAErD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB;8CAChC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,gBAAgB;8CAChC;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;;4BAAwC;0CAClC,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAEpD,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;kCAI5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCAGnE,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;gCAAa,WAAU;;kDAC1B,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;0DACxB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,QAAQ,KAAK;;;;;;;;;;;;kDAE/C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,QAAQ,WAAW;;;;;;;;;;;;+BAPf;;;;;;;;;;;;;;;;0BAgBjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAEtC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAOjC,8OAAC,uIAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/store/useDecksStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { Deck, DeckWithStats, Flashcard } from '@/types'\n\n// Mock data for demo\nconst mockDecks: DeckWithStats[] = [\n  {\n    id: '1',\n    user_id: '1',\n    title: 'Vocabulario en Inglés',\n    description: 'Palabras básicas para principiantes',\n    category: 'Idiomas',\n    tags: ['inglés', 'básico', 'vocabulario'],\n    is_public: false,\n    is_premium: false,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    card_count: 25,\n    mastered_count: 15,\n    due_count: 5,\n    last_studied: new Date(Date.now() - 86400000).toISOString() // Yesterday\n  },\n  {\n    id: '2',\n    user_id: '1',\n    title: '<PERSON><PERSON><PERSON><PERSON><PERSON> de Matemáticas',\n    description: 'Fórmulas esenciales de álgebra y geometría',\n    category: 'Matemáticas',\n    tags: ['matemáticas', 'fórmulas', 'álgebra'],\n    is_public: false,\n    is_premium: false,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    card_count: 18,\n    mastered_count: 8,\n    due_count: 10,\n    last_studied: new Date(Date.now() - 172800000).toISOString() // 2 days ago\n  },\n  {\n    id: '3',\n    user_id: '1',\n    title: 'Historia Universal',\n    description: 'Fechas y eventos importantes de la historia mundial',\n    category: 'Historia',\n    tags: ['historia', 'fechas', 'mundial'],\n    is_public: false,\n    is_premium: false,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString(),\n    card_count: 30,\n    mastered_count: 20,\n    due_count: 3,\n    last_studied: new Date(Date.now() - 259200000).toISOString() // 3 days ago\n  }\n]\n\ninterface DecksState {\n  decks: DeckWithStats[]\n  currentDeck: Deck | null\n  loading: boolean\n  fetchDecks: (userId: string) => Promise<void>\n  createDeck: (deck: Omit<Deck, 'id' | 'created_at' | 'updated_at'>) => Promise<Deck>\n  updateDeck: (id: string, updates: Partial<Deck>) => Promise<void>\n  deleteDeck: (id: string) => Promise<void>\n  setCurrentDeck: (deck: Deck | null) => void\n  getDeckStats: (deckId: string) => Promise<DeckWithStats | null>\n}\n\nexport const useDecksStore = create<DecksState>((set, get) => ({\n  decks: [],\n  currentDeck: null,\n  loading: false,\n\n  fetchDecks: async (userId: string) => {\n    set({ loading: true })\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // Return mock decks for demo\n      set({ decks: mockDecks })\n    } catch (error) {\n      console.error('Error fetching decks:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  createDeck: async (deckData) => {\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    const newDeck: DeckWithStats = {\n      id: Date.now().toString(),\n      ...deckData,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      card_count: 0,\n      mastered_count: 0,\n      due_count: 0,\n    }\n\n    set(state => ({ decks: [newDeck, ...state.decks] }))\n    return newDeck\n  },\n\n  updateDeck: async (id: string, updates: Partial<Deck>) => {\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    set(state => ({\n      decks: state.decks.map(deck =>\n        deck.id === id ? { ...deck, ...updates, updated_at: new Date().toISOString() } : deck\n      ),\n      currentDeck: state.currentDeck?.id === id\n        ? { ...state.currentDeck, ...updates, updated_at: new Date().toISOString() }\n        : state.currentDeck\n    }))\n  },\n\n  deleteDeck: async (id: string) => {\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    set(state => ({\n      decks: state.decks.filter(deck => deck.id !== id),\n      currentDeck: state.currentDeck?.id === id ? null : state.currentDeck\n    }))\n  },\n\n  setCurrentDeck: (deck: Deck | null) => {\n    set({ currentDeck: deck })\n  },\n\n  getDeckStats: async (deckId: string) => {\n    const { decks } = get()\n    return decks.find(deck => deck.id === deckId) || null\n  },\n}))\n"], "names": [], "mappings": ";;;AAAA;;AAGA,qBAAqB;AACrB,MAAM,YAA6B;IACjC;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAc;QACzC,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW,GAAG,YAAY;IAC1E;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAe;YAAY;SAAU;QAC5C,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW,GAAG,aAAa;IAC5E;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAY;YAAU;SAAU;QACvC,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW,GAAG,aAAa;IAC5E;CACD;AAcM,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,OAAO,EAAE;QACT,aAAa;QACb,SAAS;QAET,YAAY,OAAO;YACjB,IAAI;gBAAE,SAAS;YAAK;YACpB,IAAI;gBACF,oBAAoB;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,6BAA6B;gBAC7B,IAAI;oBAAE,OAAO;gBAAU;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OAAO;YACjB,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAyB;gBAC7B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,QAAQ;gBACX,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY;gBACZ,gBAAgB;gBAChB,WAAW;YACb;YAEA,IAAI,CAAA,QAAS,CAAC;oBAAE,OAAO;wBAAC;2BAAY,MAAM,KAAK;qBAAC;gBAAC,CAAC;YAClD,OAAO;QACT;QAEA,YAAY,OAAO,IAAY;YAC7B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;4BAAE,YAAY,IAAI,OAAO,WAAW;wBAAG,IAAI;oBAEnF,aAAa,MAAM,WAAW,EAAE,OAAO,KACnC;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,OAAO;wBAAE,YAAY,IAAI,OAAO,WAAW;oBAAG,IACzE,MAAM,WAAW;gBACvB,CAAC;QACH;QAEA,YAAY,OAAO;YACjB,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,CAAA,QAAS,CAAC;oBACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;oBAC9C,aAAa,MAAM,WAAW,EAAE,OAAO,KAAK,OAAO,MAAM,WAAW;gBACtE,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK;QAC1B;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,WAAW;QACnD;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/dashboard/DeckCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { DeckWithStats } from '@/types'\nimport { formatDate } from '@/lib/utils'\nimport { \n  Play, \n  Edit, \n  MoreVertical, \n  BookOpen, \n  Target, \n  Clock,\n  Share,\n  Trash2\n} from 'lucide-react'\n\ninterface DeckCardProps {\n  deck: DeckWithStats\n}\n\nexport function DeckCard({ deck }: DeckCardProps) {\n  const [showMenu, setShowMenu] = useState(false)\n\n  const progressPercentage = deck.card_count > 0 \n    ? Math.round((deck.mastered_count / deck.card_count) * 100) \n    : 0\n\n  const handleStudy = () => {\n    // Navigate to study mode\n    console.log('Study deck:', deck.id)\n  }\n\n  const handleEdit = () => {\n    // Navigate to edit mode\n    console.log('Edit deck:', deck.id)\n  }\n\n  const handleShare = () => {\n    // Share deck functionality\n    console.log('Share deck:', deck.id)\n  }\n\n  const handleDelete = () => {\n    // Delete deck functionality\n    console.log('Delete deck:', deck.id)\n  }\n\n  return (\n    <Card className=\"hover:shadow-lg transition-shadow cursor-pointer group\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <CardTitle className=\"text-lg line-clamp-2 group-hover:text-blue-600 transition-colors\">\n              {deck.title}\n            </CardTitle>\n            {deck.description && (\n              <CardDescription className=\"mt-1 line-clamp-2\">\n                {deck.description}\n              </CardDescription>\n            )}\n          </div>\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={(e) => {\n                e.stopPropagation()\n                setShowMenu(!showMenu)\n              }}\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity\"\n            >\n              <MoreVertical className=\"h-4 w-4\" />\n            </Button>\n            \n            {showMenu && (\n              <div className=\"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border z-10\">\n                <div className=\"py-1\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      handleEdit()\n                      setShowMenu(false)\n                    }}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Edit className=\"h-4 w-4 mr-2\" />\n                    Editar\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      handleShare()\n                      setShowMenu(false)\n                    }}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <Share className=\"h-4 w-4 mr-2\" />\n                    Compartir\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      handleDelete()\n                      setShowMenu(false)\n                    }}\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    Eliminar\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Tags */}\n        {deck.tags && deck.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-1 mt-2\">\n            {deck.tags.slice(0, 3).map((tag, index) => (\n              <span\n                key={index}\n                className=\"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full\"\n              >\n                {tag}\n              </span>\n            ))}\n            {deck.tags.length > 3 && (\n              <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full\">\n                +{deck.tags.length - 3}\n              </span>\n            )}\n          </div>\n        )}\n      </CardHeader>\n\n      <CardContent className=\"pt-0\">\n        {/* Stats */}\n        <div className=\"grid grid-cols-3 gap-4 mb-4\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <BookOpen className=\"h-4 w-4 text-blue-600 mr-1\" />\n              <span className=\"text-sm font-medium text-gray-900\">\n                {deck.card_count}\n              </span>\n            </div>\n            <p className=\"text-xs text-gray-500\">Tarjetas</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Target className=\"h-4 w-4 text-green-600 mr-1\" />\n              <span className=\"text-sm font-medium text-gray-900\">\n                {deck.mastered_count}\n              </span>\n            </div>\n            <p className=\"text-xs text-gray-500\">Dominadas</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Clock className=\"h-4 w-4 text-orange-600 mr-1\" />\n              <span className=\"text-sm font-medium text-gray-900\">\n                {deck.due_count}\n              </span>\n            </div>\n            <p className=\"text-xs text-gray-500\">Pendientes</p>\n          </div>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-4\">\n          <div className=\"flex items-center justify-between mb-1\">\n            <span className=\"text-sm font-medium text-gray-700\">Progreso</span>\n            <span className=\"text-sm text-gray-500\">{progressPercentage}%</span>\n          </div>\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <div\n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${progressPercentage}%` }}\n            />\n          </div>\n        </div>\n\n        {/* Action Button */}\n        <Button \n          onClick={handleStudy}\n          className=\"w-full\"\n          variant={deck.due_count > 0 ? \"default\" : \"outline\"}\n        >\n          <Play className=\"h-4 w-4 mr-2\" />\n          {deck.due_count > 0 ? `Estudiar (${deck.due_count})` : 'Repasar'}\n        </Button>\n\n        {/* Last studied */}\n        {deck.last_studied && (\n          <p className=\"text-xs text-gray-500 text-center mt-2\">\n            Último estudio: {formatDate(new Date(deck.last_studied))}\n          </p>\n        )}\n      </CardContent>\n\n      {/* Overlay for menu */}\n      {showMenu && (\n        <div\n          className=\"fixed inset-0 z-5\"\n          onClick={() => setShowMenu(false)}\n        />\n      )}\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAsBO,SAAS,SAAS,EAAE,IAAI,EAAiB;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB,KAAK,UAAU,GAAG,IACzC,KAAK,KAAK,CAAC,AAAC,KAAK,cAAc,GAAG,KAAK,UAAU,GAAI,OACrD;IAEJ,MAAM,cAAc;QAClB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,eAAe,KAAK,EAAE;IACpC;IAEA,MAAM,aAAa;QACjB,wBAAwB;QACxB,QAAQ,GAAG,CAAC,cAAc,KAAK,EAAE;IACnC;IAEA,MAAM,cAAc;QAClB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,eAAe,KAAK,EAAE;IACpC;IAEA,MAAM,eAAe;QACnB,4BAA4B;QAC5B,QAAQ,GAAG,CAAC,gBAAgB,KAAK,EAAE;IACrC;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,KAAK,KAAK;;;;;;oCAEZ,KAAK,WAAW,kBACf,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;kDACxB,KAAK,WAAW;;;;;;;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,YAAY,CAAC;wCACf;wCACA,WAAU;kDAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;oCAGzB,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB;wDACA,YAAY;oDACd;oDACA,WAAU;;sEAEV,8OAAC,2MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB;wDACA,YAAY;oDACd;oDACA,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGpC,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB;wDACA,YAAY;oDACd;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU9C,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;4BAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;gCAAK,WAAU;;oCAA2D;oCACvE,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DACb,KAAK,UAAU;;;;;;;;;;;;kDAGpB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DACb,KAAK,cAAc;;;;;;;;;;;;kDAGxB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DACb,KAAK,SAAS;;;;;;;;;;;;kDAGnB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,8OAAC;wCAAK,WAAU;;4CAAyB;4CAAmB;;;;;;;;;;;;;0CAE9D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;kCAM/C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,SAAS,KAAK,SAAS,GAAG,IAAI,YAAY;;0CAE1C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,KAAK,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG;;;;;;;oBAIxD,KAAK,YAAY,kBAChB,8OAAC;wBAAE,WAAU;;4BAAyC;4BACnC,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,KAAK,YAAY;;;;;;;;;;;;;YAM3D,0BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,YAAY;;;;;;;;;;;;AAKrC", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/dashboard/CreateDeckModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { useDecksStore } from '@/store/useDecksStore'\nimport { X, BookOpen, Tag, FileText } from 'lucide-react'\nimport { LoadingSpinner } from '@/components/ui/loading-spinner'\n\ninterface CreateDeckModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function CreateDeckModal({ isOpen, onClose }: CreateDeckModalProps) {\n  const [title, setTitle] = useState('')\n  const [description, setDescription] = useState('')\n  const [category, setCategory] = useState('')\n  const [tags, setTags] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { user } = useAuthStore()\n  const { createDeck } = useDecksStore()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!user) return\n\n    setLoading(true)\n    setError('')\n\n    try {\n      const tagsArray = tags\n        .split(',')\n        .map(tag => tag.trim())\n        .filter(tag => tag.length > 0)\n\n      await createDeck({\n        user_id: user.id,\n        title: title.trim(),\n        description: description.trim() || null,\n        category: category.trim() || null,\n        tags: tagsArray,\n        is_public: false,\n        is_premium: false,\n      })\n\n      // Reset form\n      setTitle('')\n      setDescription('')\n      setCategory('')\n      setTags('')\n      onClose()\n    } catch (err: any) {\n      setError(err.message || 'Ha ocurrido un error al crear el mazo')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const categories = [\n    'Idiomas',\n    'Ciencias',\n    'Matemáticas',\n    'Historia',\n    'Literatura',\n    'Medicina',\n    'Programación',\n    'Arte',\n    'Música',\n    'Deportes',\n    'Geografía',\n    'Filosofía',\n    'Otro'\n  ]\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <Card className=\"w-full max-w-lg max-h-[90vh] overflow-y-auto\">\n        <CardHeader className=\"relative\">\n          <button\n            onClick={onClose}\n            className=\"absolute right-4 top-4 text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n          <CardTitle className=\"text-2xl flex items-center\">\n            <BookOpen className=\"h-6 w-6 mr-2 text-blue-600\" />\n            Crear Nuevo Mazo\n          </CardTitle>\n          <CardDescription>\n            Crea un nuevo mazo de flashcards para organizar tu aprendizaje\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {/* Title */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"title\" className=\"text-sm font-medium\">\n                Título del Mazo *\n              </label>\n              <div className=\"relative\">\n                <FileText className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"title\"\n                  type=\"text\"\n                  placeholder=\"Ej: Vocabulario en Inglés\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  className=\"pl-10\"\n                  required\n                  maxLength={100}\n                />\n              </div>\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"description\" className=\"text-sm font-medium\">\n                Descripción (Opcional)\n              </label>\n              <textarea\n                id=\"description\"\n                placeholder=\"Describe brevemente el contenido de este mazo...\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                rows={3}\n                maxLength={500}\n              />\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"category\" className=\"text-sm font-medium\">\n                Categoría (Opcional)\n              </label>\n              <select\n                id=\"category\"\n                value={category}\n                onChange={(e) => setCategory(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">Seleccionar categoría...</option>\n                {categories.map((cat) => (\n                  <option key={cat} value={cat}>\n                    {cat}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Tags */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"tags\" className=\"text-sm font-medium\">\n                Etiquetas (Opcional)\n              </label>\n              <div className=\"relative\">\n                <Tag className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"tags\"\n                  type=\"text\"\n                  placeholder=\"Ej: básico, principiante, examen (separadas por comas)\"\n                  value={tags}\n                  onChange={(e) => setTags(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              <p className=\"text-xs text-gray-500\">\n                Separa las etiquetas con comas para facilitar la búsqueda\n              </p>\n            </div>\n\n            {error && (\n              <div className=\"text-red-600 text-sm text-center bg-red-50 p-2 rounded\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"flex space-x-3 pt-4\">\n              <Button \n                type=\"button\" \n                variant=\"outline\" \n                onClick={onClose}\n                className=\"flex-1\"\n                disabled={loading}\n              >\n                Cancelar\n              </Button>\n              <Button \n                type=\"submit\" \n                className=\"flex-1\" \n                disabled={loading || !title.trim()}\n              >\n                {loading ? (\n                  <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                ) : null}\n                Crear Mazo\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAgBO,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAwB;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,YAAY,KACf,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IACnB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;YAE9B,MAAM,WAAW;gBACf,SAAS,KAAK,EAAE;gBAChB,OAAO,MAAM,IAAI;gBACjB,aAAa,YAAY,IAAI,MAAM;gBACnC,UAAU,SAAS,IAAI,MAAM;gBAC7B,MAAM;gBACN,WAAW;gBACX,YAAY;YACd;YAEA,aAAa;YACb,SAAS;YACT,eAAe;YACf,YAAY;YACZ,QAAQ;YACR;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAGrD,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAsB;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;gDACV,QAAQ;gDACR,WAAW;;;;;;;;;;;;;;;;;;0CAMjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAsB;;;;;;kDAG7D,8OAAC;wCACC,IAAG;wCACH,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,MAAM;wCACN,WAAW;;;;;;;;;;;;0CAKf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAsB;;;;;;kDAG1D,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC;oDAAiB,OAAO;8DACtB;mDADU;;;;;;;;;;;;;;;;;0CAQnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAO,WAAU;kDAAsB;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACvC,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;4BAKtC,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;wCACV,UAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU,WAAW,CAAC,MAAM,IAAI;;4CAE/B,wBACC,8OAAC,8IAAA,CAAA,iBAAc;gDAAC,MAAK;gDAAK,WAAU;;;;;uDAClC;4CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzB", "debugId": null}}, {"offset": {"line": 2211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/dashboard/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { Button } from '@/components/ui/button'\nimport { \n  BookOpen, \n  User, \n  Settings, \n  LogOut,\n  Bell,\n  Search,\n  Menu,\n  X\n} from 'lucide-react'\n\nexport function Navbar() {\n  const { user, profile, signOut } = useAuthStore()\n  const [showUserMenu, setShowUserMenu] = useState(false)\n  const [showMobileMenu, setShowMobileMenu] = useState(false)\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const navigation = [\n    { name: 'Dashboard', href: '/', current: true },\n    { name: '<PERSON><PERSON> Mazos', href: '/decks', current: false },\n    { name: 'Explorar', href: '/explore', current: false },\n    { name: 'Estadísticas', href: '/stats', current: false },\n  ]\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">FlashCards Pro</span>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                  item.current\n                    ? 'text-blue-600 bg-blue-50'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                {item.name}\n              </a>\n            ))}\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\n            <div className=\"relative w-full\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Buscar mazos...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full\"></span>\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                onClick={() => setShowUserMenu(!showUserMenu)}\n                className=\"flex items-center space-x-2\"\n              >\n                <div className=\"h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">\n                    {profile?.full_name?.[0] || user?.email?.[0] || 'U'}\n                  </span>\n                </div>\n                <span className=\"hidden md:block text-sm font-medium\">\n                  {profile?.full_name || user?.email}\n                </span>\n              </Button>\n\n              {/* User Dropdown */}\n              {showUserMenu && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border z-50\">\n                  <div className=\"py-1\">\n                    <div className=\"px-4 py-2 border-b\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {profile?.full_name || 'Usuario'}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">{user?.email}</p>\n                    </div>\n                    <a\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <User className=\"h-4 w-4 mr-2\" />\n                      Mi Perfil\n                    </a>\n                    <a\n                      href=\"/settings\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      Configuración\n                    </a>\n                    <button\n                      onClick={handleSignOut}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <LogOut className=\"h-4 w-4 mr-2\" />\n                      Cerrar Sesión\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Mobile menu button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden\"\n              onClick={() => setShowMobileMenu(!showMobileMenu)}\n            >\n              {showMobileMenu ? (\n                <X className=\"h-5 w-5\" />\n              ) : (\n                <Menu className=\"h-5 w-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {showMobileMenu && (\n          <div className=\"md:hidden border-t\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navigation.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  className={`block px-3 py-2 text-base font-medium rounded-md ${\n                    item.current\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  {item.name}\n                </a>\n              ))}\n            </div>\n            {/* Mobile Search */}\n            <div className=\"px-2 pb-3\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Buscar mazos...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Overlay for user menu */}\n      {showUserMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAK,SAAS;QAAK;QAC9C;YAAE,MAAM;YAAa,MAAM;YAAU,SAAS;QAAM;QACpD;YAAE,MAAM;YAAY,MAAM;YAAY,SAAS;QAAM;QACrD;YAAE,MAAM;YAAgB,MAAM;YAAU,SAAS;QAAM;KACxD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,2DAA2D,EACrE,KAAK,OAAO,GACR,6BACA,sDACJ;kDAED,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;;;;;;0CAcpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;;0DAC5C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAIlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB,CAAC;gDAChC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,SAAS,WAAW,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,EAAE,IAAI;;;;;;;;;;;kEAGpD,8OAAC;wDAAK,WAAU;kEACb,SAAS,aAAa,MAAM;;;;;;;;;;;;4CAKhC,8BACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,SAAS,aAAa;;;;;;8EAEzB,8OAAC;oEAAE,WAAU;8EAAyB,MAAM;;;;;;;;;;;;sEAE9C,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,8OAAC;4DACC,MAAK;4DACL,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAS7C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB,CAAC;kDAEjC,+BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAOvB,gCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,iDAAiD,EAC3D,KAAK,OAAO,GACR,6BACA,sDACJ;kDAED,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;;;;;;0CAapB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrB,8BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/dashboard/StatsOverview.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { useDecksStore } from '@/store/useDecksStore'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  BookOpen, \n  Brain, \n  Target, \n  Clock,\n  TrendingUp,\n  Calendar\n} from 'lucide-react'\n\nexport function StatsOverview() {\n  const { user } = useAuthStore()\n  const { decks } = useDecksStore()\n  const [stats, setStats] = useState({\n    totalDecks: 0,\n    totalCards: 0,\n    masteredCards: 0,\n    dueCards: 0,\n    studyStreak: 0,\n    timeStudied: 0\n  })\n\n  useEffect(() => {\n    if (decks.length > 0) {\n      const totalCards = decks.reduce((sum, deck) => sum + deck.card_count, 0)\n      const masteredCards = decks.reduce((sum, deck) => sum + deck.mastered_count, 0)\n      const dueCards = decks.reduce((sum, deck) => sum + deck.due_count, 0)\n\n      setStats({\n        totalDecks: decks.length,\n        totalCards,\n        masteredCards,\n        dueCards,\n        studyStreak: 7, // Mock data - would come from backend\n        timeStudied: 120 // Mock data - minutes studied this week\n      })\n    }\n  }, [decks])\n\n  const statCards = [\n    {\n      title: \"Mazos Totales\",\n      value: stats.totalDecks,\n      icon: BookOpen,\n      color: \"text-blue-600\",\n      bgColor: \"bg-blue-50\"\n    },\n    {\n      title: \"Tarjetas Totales\",\n      value: stats.totalCards,\n      icon: Brain,\n      color: \"text-green-600\",\n      bgColor: \"bg-green-50\"\n    },\n    {\n      title: \"Dominadas\",\n      value: stats.masteredCards,\n      icon: Target,\n      color: \"text-purple-600\",\n      bgColor: \"bg-purple-50\"\n    },\n    {\n      title: \"Pendientes\",\n      value: stats.dueCards,\n      icon: Clock,\n      color: \"text-orange-600\",\n      bgColor: \"bg-orange-50\"\n    },\n    {\n      title: \"Racha de Días\",\n      value: stats.studyStreak,\n      icon: TrendingUp,\n      color: \"text-red-600\",\n      bgColor: \"bg-red-50\"\n    },\n    {\n      title: \"Min. Esta Semana\",\n      value: stats.timeStudied,\n      icon: Calendar,\n      color: \"text-indigo-600\",\n      bgColor: \"bg-indigo-50\"\n    }\n  ]\n\n  return (\n    <section className=\"mb-8\">\n      <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">Resumen de Progreso</h2>\n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n        {statCards.map((stat, index) => (\n          <Card key={index}>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 mb-1\">\n                    {stat.title}\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {stat.value}\n                  </p>\n                </div>\n                <div className={`${stat.bgColor} p-2 rounded-lg`}>\n                  <stat.icon className={`h-5 w-5 ${stat.color}`} />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,UAAU;QACV,aAAa;QACb,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;YACtE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE;YAC7E,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;YAEnE,SAAS;gBACP,YAAY,MAAM,MAAM;gBACxB;gBACA;gBACA;gBACA,aAAa;gBACb,aAAa,IAAI,wCAAwC;YAC3D;QACF;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,QAAQ;YACrB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW;YACxB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW;YACxB,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;;;;;;;kDAGf,8OAAC;wCAAI,WAAW,GAAG,KAAK,OAAO,CAAC,eAAe,CAAC;kDAC9C,cAAA,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;uBAZ1C;;;;;;;;;;;;;;;;AAqBrB", "debugId": null}}, {"offset": {"line": 2815, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/components/dashboard/Dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { useDecksStore } from '@/store/useDecksStore'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { DeckCard } from './DeckCard'\nimport { CreateDeckModal } from './CreateDeckModal'\nimport { Navbar } from './Navbar'\nimport { StatsOverview } from './StatsOverview'\nimport { \n  Plus, \n  BookOpen, \n  Brain, \n  Trophy,\n  Clock\n} from 'lucide-react'\n\nexport function Dashboard() {\n  const { user, profile } = useAuthStore()\n  const { decks, loading, fetchDecks } = useDecksStore()\n  const [showCreateModal, setShowCreateModal] = useState(false)\n\n  useEffect(() => {\n    if (user) {\n      fetchDecks(user.id)\n    }\n  }, [user, fetchDecks])\n\n  const quickActions = [\n    {\n      icon: Plus,\n      title: \"<PERSON><PERSON><PERSON> Mazo\",\n      description: \"Crea un nuevo mazo de flashcards\",\n      action: () => setShowCreateModal(true),\n      color: \"bg-blue-500\"\n    },\n    {\n      icon: Brain,\n      title: \"Estudio Rápido\",\n      description: \"Repasa tarjetas pendientes\",\n      action: () => console.log('Quick study'),\n      color: \"bg-green-500\"\n    },\n    {\n      icon: Trophy,\n      title: \"Juegos\",\n      description: \"Practica con mini-juegos\",\n      action: () => console.log('Games'),\n      color: \"bg-purple-500\"\n    },\n    {\n      icon: Clock,\n      title: \"Sesión Cronometrada\",\n      description: \"Estudio con temporizador\",\n      action: () => console.log('Timed session'),\n      color: \"bg-orange-500\"\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            ¡Hola, {profile?.full_name || user?.email}! 👋\n          </h1>\n          <p className=\"text-gray-600\">\n            Continúa tu viaje de aprendizaje. Tienes {decks.filter(d => d.due_count > 0).length} mazos con tarjetas pendientes.\n          </p>\n        </div>\n\n        {/* Stats Overview */}\n        <StatsOverview />\n\n        {/* Quick Actions */}\n        <section className=\"mb-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-900 mb-4\">Acciones Rápidas</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {quickActions.map((action, index) => (\n              <Card \n                key={index} \n                className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n                onClick={action.action}\n              >\n                <CardContent className=\"p-6 text-center\">\n                  <div className={`${action.color} w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3`}>\n                    <action.icon className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h3 className=\"font-semibold text-gray-900 mb-1\">{action.title}</h3>\n                  <p className=\"text-sm text-gray-600\">{action.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* My Decks */}\n        <section>\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900\">Mis Mazos</h2>\n            <Button onClick={() => setShowCreateModal(true)}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuevo Mazo\n            </Button>\n          </div>\n\n          {loading ? (\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {[...Array(6)].map((_, i) => (\n                <Card key={i} className=\"animate-pulse\">\n                  <CardHeader>\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"h-20 bg-gray-200 rounded\"></div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : decks.length > 0 ? (\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {decks.map((deck) => (\n                <DeckCard key={deck.id} deck={deck} />\n              ))}\n            </div>\n          ) : (\n            <Card className=\"text-center py-12\">\n              <CardContent>\n                <BookOpen className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  No tienes mazos aún\n                </h3>\n                <p className=\"text-gray-600 mb-6\">\n                  Crea tu primer mazo de flashcards para comenzar a aprender\n                </p>\n                <Button onClick={() => setShowCreateModal(true)}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Crear Mi Primer Mazo\n                </Button>\n              </CardContent>\n            </Card>\n          )}\n        </section>\n      </main>\n\n      {/* Create Deck Modal */}\n      <CreateDeckModal \n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,WAAW,KAAK,EAAE;QACpB;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,eAAe;QACnB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,QAAQ,IAAM,mBAAmB;YACjC,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC1B,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC1B,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC1B,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAwC;oCAC5C,SAAS,aAAa,MAAM;oCAAM;;;;;;;0CAE5C,8OAAC;gCAAE,WAAU;;oCAAgB;oCACe,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,GAAG,MAAM;oCAAC;;;;;;;;;;;;;kCAKxF,8OAAC,gJAAA,CAAA,gBAAa;;;;;kCAGd,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;wCACV,SAAS,OAAO,MAAM;kDAEtB,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAW,GAAG,OAAO,KAAK,CAAC,mEAAmE,CAAC;8DAClG,cAAA,8OAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;oDAAG,WAAU;8DAAoC,OAAO,KAAK;;;;;;8DAC9D,8OAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;uCATrD;;;;;;;;;;;;;;;;kCAiBb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,mBAAmB;;0DACxC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKpC,wBACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;wCAAS,WAAU;;0DACtB,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEjB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;uCANR;;;;;;;;;uCAWb,MAAM,MAAM,GAAG,kBACjB,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,2IAAA,CAAA,WAAQ;wCAAe,MAAM;uCAAf,KAAK,EAAE;;;;;;;;;qDAI1B,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,mBAAmB;;8DACxC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC,kJAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Cosas%20de%20programacion/flashcards/flashcards-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuthStore } from '@/store/useAuthStore'\nimport { LandingPage } from '@/components/landing/LandingPage'\nimport { Dashboard } from '@/components/dashboard/Dashboard'\nimport { LoadingSpinner } from '@/components/ui/loading-spinner'\n\nexport default function Home() {\n  const { user, loading } = useAuthStore()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  return user ? <Dashboard /> : <LandingPage />\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAErC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,OAAO,qBAAO,8OAAC,4IAAA,CAAA,YAAS;;;;6BAAM,8OAAC,4IAAA,CAAA,cAAW;;;;;AAC5C", "debugId": null}}]}