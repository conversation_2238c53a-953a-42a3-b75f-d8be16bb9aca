'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/useAuthStore'
import { useDecksStore } from '@/store/useDecksStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DeckCard } from './DeckCard'
import { CreateDeckModal } from './CreateDeckModal'
import { Navbar } from './Navbar'
import { StatsOverview } from './StatsOverview'
import { 
  Plus, 
  BookOpen, 
  Brain, 
  Trophy,
  Clock
} from 'lucide-react'

export function Dashboard() {
  const { user, profile } = useAuthStore()
  const { decks, loading, fetchDecks } = useDecksStore()
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    if (user) {
      fetchDecks(user.id)
    }
  }, [user, fetchDecks])

  const quickActions = [
    {
      icon: Plus,
      title: "<PERSON><PERSON><PERSON> Mazo",
      description: "Crea un nuevo mazo de flashcards",
      action: () => setShowCreateModal(true),
      color: "bg-blue-500"
    },
    {
      icon: Brain,
      title: "Estudio Rápido",
      description: "Repasa tarjetas pendientes",
      action: () => console.log('Quick study'),
      color: "bg-green-500"
    },
    {
      icon: Trophy,
      title: "Juegos",
      description: "Practica con mini-juegos",
      action: () => console.log('Games'),
      color: "bg-purple-500"
    },
    {
      icon: Clock,
      title: "Sesión Cronometrada",
      description: "Estudio con temporizador",
      action: () => console.log('Timed session'),
      color: "bg-orange-500"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            ¡Hola, {profile?.full_name || user?.email}! 👋
          </h1>
          <p className="text-gray-600">
            Continúa tu viaje de aprendizaje. Tienes {decks.filter(d => d.due_count > 0).length} mazos con tarjetas pendientes.
          </p>
        </div>

        {/* Stats Overview */}
        <StatsOverview />

        {/* Quick Actions */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Acciones Rápidas</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Card 
                key={index} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={action.action}
              >
                <CardContent className="p-6 text-center">
                  <div className={`${action.color} w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{action.title}</h3>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* My Decks */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-gray-900">Mis Mazos</h2>
            <Button onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Mazo
            </Button>
          </div>

          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-20 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : decks.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {decks.map((deck) => (
                <DeckCard key={deck.id} deck={deck} />
              ))}
            </div>
          ) : (
            <Card className="text-center py-12">
              <CardContent>
                <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No tienes mazos aún
                </h3>
                <p className="text-gray-600 mb-6">
                  Crea tu primer mazo de flashcards para comenzar a aprender
                </p>
                <Button onClick={() => setShowCreateModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Crear Mi Primer Mazo
                </Button>
              </CardContent>
            </Card>
          )}
        </section>
      </main>

      {/* Create Deck Modal */}
      <CreateDeckModal 
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  )
}
