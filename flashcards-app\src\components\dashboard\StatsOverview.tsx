'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/useAuthStore'
import { useDecksStore } from '@/store/useDecksStore'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  BookOpen, 
  Brain, 
  Target, 
  Clock,
  TrendingUp,
  Calendar
} from 'lucide-react'

export function StatsOverview() {
  const { user } = useAuthStore()
  const { decks } = useDecksStore()
  const [stats, setStats] = useState({
    totalDecks: 0,
    totalCards: 0,
    masteredCards: 0,
    dueCards: 0,
    studyStreak: 0,
    timeStudied: 0
  })

  useEffect(() => {
    if (decks.length > 0) {
      const totalCards = decks.reduce((sum, deck) => sum + deck.card_count, 0)
      const masteredCards = decks.reduce((sum, deck) => sum + deck.mastered_count, 0)
      const dueCards = decks.reduce((sum, deck) => sum + deck.due_count, 0)

      setStats({
        totalDecks: decks.length,
        totalCards,
        masteredCards,
        dueCards,
        studyStreak: 7, // Mock data - would come from backend
        timeStudied: 120 // Mock data - minutes studied this week
      })
    }
  }, [decks])

  const statCards = [
    {
      title: "Mazos Totales",
      value: stats.totalDecks,
      icon: BookOpen,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Tarjetas Totales",
      value: stats.totalCards,
      icon: Brain,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "Dominadas",
      value: stats.masteredCards,
      icon: Target,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "Pendientes",
      value: stats.dueCards,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      title: "Racha de Días",
      value: stats.studyStreak,
      icon: TrendingUp,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      title: "Min. Esta Semana",
      value: stats.timeStudied,
      icon: Calendar,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50"
    }
  ]

  return (
    <section className="mb-8">
      <h2 className="text-2xl font-semibold text-gray-900 mb-4">Resumen de Progreso</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`${stat.bgColor} p-2 rounded-lg`}>
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}
