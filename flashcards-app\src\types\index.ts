import { Database } from './database'

export type Profile = Database['public']['Tables']['profiles']['Row']
export type Deck = Database['public']['Tables']['decks']['Row']
export type Flashcard = Database['public']['Tables']['flashcards']['Row']
export type StudySession = Database['public']['Tables']['study_sessions']['Row']
export type CardProgress = Database['public']['Tables']['card_progress']['Row']

export type DeckWithStats = Deck & {
  card_count: number
  mastered_count: number
  due_count: number
  last_studied?: string
}

export type FlashcardWithProgress = Flashcard & {
  progress?: CardProgress
}

export interface StudyStats {
  totalCards: number
  masteredCards: number
  dueCards: number
  streakDays: number
  totalTimeStudied: number
  averageAccuracy: number
}

export interface GameResult {
  score: number
  timeSpent: number
  correctAnswers: number
  totalQuestions: number
  gameType: 'matching' | 'multiple_choice' | 'time_challenge'
}

export interface QuizQuestion {
  id: string
  question: string
  options: string[]
  correctAnswer: string
  explanation?: string
  cardId: string
}

export interface StudyMode {
  id: string
  name: string
  description: string
  icon: string
  color: string
}

export interface NotificationSettings {
  dailyReminder: boolean
  reminderTime: string
  weeklyReport: boolean
  achievementNotifications: boolean
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  studyReminders: NotificationSettings
  autoPlayAudio: boolean
  showAnswerDelay: number
}

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt?: string
  progress: number
  maxProgress: number
}

export interface DeckTemplate {
  id: string
  name: string
  description: string
  category: string
  cardCount: number
  preview: Flashcard[]
  isPremium: boolean
}
