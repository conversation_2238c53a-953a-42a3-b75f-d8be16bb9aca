import { Book<PERSON><PERSON>, Brain, Trophy, Zap, Users, Target } from 'lucide-react'

interface LandingPageProps {
  onGetStarted: () => void
}

export function LandingPage({ onGetStarted }: LandingPageProps) {
  const features = [
    {
      icon: <Brain size={48} />,
      title: 'Repetición Espaciada',
      description: 'Algoritmo inteligente que optimiza tu aprendizaje mostrando las tarjetas en el momento perfecto.'
    },
    {
      icon: <BookOpen size={48} />,
      title: 'Mazos Organizados',
      description: 'Organiza tus flashcards en mazos temáticos para un estudio más eficiente.'
    },
    {
      icon: <Trophy size={48} />,
      title: 'Seguimiento de Progreso',
      description: 'Monitorea tu avance y identifica las áreas que necesitan más práctica.'
    },
    {
      icon: <Zap size={48} />,
      title: 'Estudio Rápido',
      description: 'Interfaz intuitiva que te permite estudiar de manera eficiente y sin distracciones.'
    },
    {
      icon: <Users size={48} />,
      title: 'Mazos Compartidos',
      description: 'Accede a mazos creados por otros usuarios o comparte los tuyos.'
    },
    {
      icon: <Target size={48} />,
      title: 'Objetivos Personalizados',
      description: 'Establece metas de estudio y mantén la motivación con estadísticas detalladas.'
    }
  ]

  return (
    <div style={{ textAlign: 'center', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Hero Section */}
      <div style={{ marginBottom: '4rem' }}>
        <div style={{ 
          fontSize: '4rem', 
          marginBottom: '1rem',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          🎓
        </div>
        <h1 style={{ 
          fontSize: '3.5rem', 
          fontWeight: 'bold', 
          color: 'white', 
          marginBottom: '1rem',
          textShadow: '0 2px 4px rgba(0,0,0,0.3)'
        }}>
          FlashCards Pro
        </h1>
        <p style={{ 
          fontSize: '1.5rem', 
          color: 'rgba(255, 255, 255, 0.9)', 
          marginBottom: '2rem',
          maxWidth: '600px',
          margin: '0 auto 2rem auto',
          lineHeight: '1.6'
        }}>
          La aplicación de flashcards más completa para maximizar tu aprendizaje con repetición espaciada y seguimiento inteligente.
        </p>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button 
            className="btn btn-primary"
            onClick={onGetStarted}
            style={{ fontSize: '1.1rem', padding: '1rem 2rem' }}
          >
            <BookOpen size={20} />
            Comenzar Gratis
          </button>
          <button 
            className="btn btn-secondary"
            style={{ fontSize: '1.1rem', padding: '1rem 2rem' }}
          >
            <Trophy size={20} />
            Ver Demo
          </button>
        </div>
      </div>

      {/* Features Grid */}
      <div className="features-grid">
        {features.map((feature, index) => (
          <div key={index} className="feature-card">
            <div className="feature-icon" style={{ color: '#667eea' }}>
              {feature.icon}
            </div>
            <h3 className="feature-title">{feature.title}</h3>
            <p className="feature-description">{feature.description}</p>
          </div>
        ))}
      </div>

      {/* Stats Section */}
      <div className="card" style={{ margin: '4rem auto', maxWidth: '800px' }}>
        <h2 style={{ 
          fontSize: '2rem', 
          marginBottom: '2rem', 
          color: '#1f2937',
          fontWeight: 'bold'
        }}>
          Únete a miles de estudiantes exitosos
        </h2>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
          gap: '2rem',
          textAlign: 'center'
        }}>
          <div>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#667eea' }}>10K+</div>
            <div style={{ color: '#6b7280' }}>Usuarios Activos</div>
          </div>
          <div>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#667eea' }}>1M+</div>
            <div style={{ color: '#6b7280' }}>Flashcards Creadas</div>
          </div>
          <div>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#667eea' }}>95%</div>
            <div style={{ color: '#6b7280' }}>Mejora en Retención</div>
          </div>
          <div>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#667eea' }}>50+</div>
            <div style={{ color: '#6b7280' }}>Idiomas Soportados</div>
          </div>
        </div>
      </div>

      {/* CTA Final */}
      <div style={{ margin: '4rem 0', color: 'white' }}>
        <h2 style={{ fontSize: '2.5rem', marginBottom: '1rem', fontWeight: 'bold' }}>
          ¿Listo para revolucionar tu aprendizaje?
        </h2>
        <p style={{ fontSize: '1.2rem', marginBottom: '2rem', opacity: 0.9 }}>
          Comienza gratis y descubre el poder de la repetición espaciada
        </p>
        <button 
          className="btn btn-primary"
          onClick={onGetStarted}
          style={{ fontSize: '1.2rem', padding: '1rem 2.5rem' }}
        >
          <Zap size={20} />
          Empezar Ahora
        </button>
      </div>
    </div>
  )
}
