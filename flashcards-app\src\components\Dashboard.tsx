import { Play, Plus, BookO<PERSON>, <PERSON>, <PERSON>, Calendar } from 'lucide-react'
import { Deck } from './FlashcardApp'

interface DashboardProps {
  decks: Deck[]
  onStartStudy: (deck: Deck) => void
  onCreateDeck: () => void
  onViewStats: () => void
}

export function Dashboard({ decks, onStartStudy, onCreateDeck, onViewStats }: DashboardProps) {
  const totalCards = decks.reduce((sum, deck) => sum + deck.cards.length, 0)
  const totalDecks = decks.length

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', width: '100%' }}>
      {/* Welcome Section */}
      <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{ 
          fontSize: '2.5rem', 
          color: 'white', 
          marginBottom: '0.5rem',
          fontWeight: 'bold'
        }}>
          ¡Bienvenido de vuelta! 👋
        </h1>
        <p style={{ 
          fontSize: '1.2rem', 
          color: 'rgba(255, 255, 255, 0.8)',
          marginBottom: '2rem'
        }}>
          Continúa tu viaje de aprendizaje
        </p>
      </div>

      {/* Stats Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
        gap: '1.5rem',
        marginBottom: '3rem'
      }}>
        <div className="card" style={{ textAlign: 'center' }}>
          <BookOpen size={32} style={{ color: '#667eea', marginBottom: '1rem' }} />
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>{totalDecks}</div>
          <div style={{ color: '#6b7280' }}>Mazos Totales</div>
        </div>
        <div className="card" style={{ textAlign: 'center' }}>
          <Brain size={32} style={{ color: '#667eea', marginBottom: '1rem' }} />
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>{totalCards}</div>
          <div style={{ color: '#6b7280' }}>Tarjetas Totales</div>
        </div>
        <div className="card" style={{ textAlign: 'center' }}>
          <Trophy size={32} style={{ color: '#667eea', marginBottom: '1rem' }} />
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>7</div>
          <div style={{ color: '#6b7280' }}>Días de Racha</div>
        </div>
        <div className="card" style={{ textAlign: 'center' }}>
          <Calendar size={32} style={{ color: '#667eea', marginBottom: '1rem' }} />
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>85%</div>
          <div style={{ color: '#6b7280' }}>Precisión</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card" style={{ marginBottom: '3rem' }}>
        <h2 style={{ 
          fontSize: '1.5rem', 
          marginBottom: '1.5rem', 
          color: '#1f2937',
          fontWeight: 'bold'
        }}>
          Acciones Rápidas
        </h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <button 
            className="btn btn-primary"
            onClick={onCreateDeck}
          >
            <Plus size={16} />
            Crear Nuevo Mazo
          </button>
          <button 
            className="btn btn-secondary"
            onClick={onViewStats}
            style={{ color: '#374151' }}
          >
            <Trophy size={16} />
            Ver Estadísticas
          </button>
        </div>
      </div>

      {/* Decks Grid */}
      <div>
        <h2 style={{ 
          fontSize: '1.5rem', 
          marginBottom: '1.5rem', 
          color: 'white',
          fontWeight: 'bold'
        }}>
          Mis Mazos
        </h2>
        {decks.length === 0 ? (
          <div className="card" style={{ textAlign: 'center', padding: '3rem' }}>
            <BookOpen size={48} style={{ color: '#9ca3af', marginBottom: '1rem' }} />
            <h3 style={{ fontSize: '1.25rem', marginBottom: '0.5rem', color: '#1f2937' }}>
              No tienes mazos aún
            </h3>
            <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
              Crea tu primer mazo para comenzar a estudiar
            </p>
            <button 
              className="btn btn-primary"
              onClick={onCreateDeck}
            >
              <Plus size={16} />
              Crear Mi Primer Mazo
            </button>
          </div>
        ) : (
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', 
            gap: '1.5rem'
          }}>
            {decks.map((deck) => (
              <div key={deck.id} className="card" style={{ position: 'relative' }}>
                <h3 style={{ 
                  fontSize: '1.25rem', 
                  fontWeight: 'bold', 
                  marginBottom: '0.5rem',
                  color: '#1f2937'
                }}>
                  {deck.title}
                </h3>
                <p style={{ 
                  color: '#6b7280', 
                  marginBottom: '1rem',
                  lineHeight: '1.5'
                }}>
                  {deck.description}
                </p>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <span style={{ color: '#6b7280', fontSize: '0.9rem' }}>
                    {deck.cards.length} tarjetas
                  </span>
                  <span style={{ color: '#6b7280', fontSize: '0.9rem' }}>
                    Creado: {deck.created.toLocaleDateString()}
                  </span>
                </div>
                <button 
                  className="btn btn-primary"
                  onClick={() => onStartStudy(deck)}
                  style={{ width: '100%' }}
                >
                  <Play size={16} />
                  Estudiar Ahora
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
